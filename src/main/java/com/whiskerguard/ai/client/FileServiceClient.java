package com.whiskerguard.ai.client;

import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 文件服务客户端接口
 * <p>
 * 该接口使用 Spring Cloud OpenFeign 实现微服务间的远程调用，
 * 用于与文件服务微服务进行通信，主要提供腾讯云COS文件读取功能。
 * 
 * 主要功能：
 * 1. 根据文件名读取腾讯云COS中的文件内容
 * 2. 支持多租户数据隔离
 * 3. 提供文件存在性检查
 * 4. 支持多种文件格式的内容读取
 * 
 * <AUTHOR>
 * @since 1.0
 */
@FeignClient(name = "whiskerguard-file-service", configuration = FileServiceClient.FileServiceClientConfiguration.class)
public interface FileServiceClient {

    /**
     * 根据文件名读取腾讯云COS中的文件内容
     * <p>
     * 通过文件名从腾讯云COS读取文件内容，支持文本文件、PDF、Word等格式。
     * 返回的内容已经过格式转换，可以直接用于AI分析。
     * 
     * @param cosFileName 腾讯云COS中的文件名（包含路径）
     * @param tenantId 租户ID，用于多租户数据隔离和权限控制
     * @return 文件的文本内容，如果是二进制文件会转换为文本格式
     */
    @GetMapping("/api/files/cos/content/{cosFileName}")
    String readFileContent(
        @PathVariable("cosFileName") String cosFileName,
        @RequestParam("tenantId") Long tenantId
    );

    /**
     * 检查腾讯云COS中的文件是否存在
     * <p>
     * 验证指定的文件名在腾讯云COS中是否存在，
     * 用于在读取文件内容前进行预检查。
     * 
     * @param cosFileName 腾讯云COS中的文件名（包含路径）
     * @param tenantId 租户ID，用于多租户数据隔离
     * @return 文件是否存在的布尔值
     */
    @GetMapping("/api/files/cos/exists/{cosFileName}")
    Boolean checkFileExists(
        @PathVariable("cosFileName") String cosFileName,
        @RequestParam("tenantId") Long tenantId
    );

    /**
     * 获取文件的基本信息
     * <p>
     * 获取文件的元数据信息，包括文件大小、类型、创建时间等。
     * 用于在处理文件前了解文件的基本属性。
     * 
     * @param cosFileName 腾讯云COS中的文件名（包含路径）
     * @param tenantId 租户ID，用于多租户数据隔离
     * @return 文件信息的JSON字符串
     */
    @GetMapping("/api/files/cos/info/{cosFileName}")
    String getFileInfo(
        @PathVariable("cosFileName") String cosFileName,
        @RequestParam("tenantId") Long tenantId
    );

    /**
     * 文件服务客户端专用配置类
     * <p>
     * 配置认证拦截器和超时设置，
     * 优化文件读取操作的性能和可靠性。
     */
    @Configuration
    class FileServiceClientConfiguration {

        /**
         * 配置用户认证拦截器
         * 自动传递JWT token和用户信息
         */
        @Bean
        public UserFeignClientInterceptor userFeignClientInterceptor() {
            return new UserFeignClientInterceptor();
        }

        /**
         * 配置Feign请求选项
         * 文件读取可能需要较长时间，特别是大文件
         */
        @Bean
        public Request.Options feignRequestOptions() {
            return new Request.Options(
                5000,  // 连接超时 5 秒
                30000  // 读取超时 30 秒 - 文件读取可能需要较长时间
            );
        }
    }
}
