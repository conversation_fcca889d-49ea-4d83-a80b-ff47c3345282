package com.whiskerguard.ai.service.prompt.impl;

import com.whiskerguard.ai.config.SystemRoleProperties;
import com.whiskerguard.ai.domain.PromptTemplate;
import com.whiskerguard.ai.domain.PromptTemplateVariable;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateStatus;
import com.whiskerguard.ai.domain.enumeration.PromptTemplateType;
import com.whiskerguard.ai.repository.PromptTemplateRepository;
import com.whiskerguard.ai.service.invocation.RagHelper;
import com.whiskerguard.ai.service.prompt.*;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 提示词构建服务实现
 * <p>
 * 提供完整的提示词构建功能，包括模板查找、变量解析、
 * RAG增强、缓存管理等核心功能。
 */
@Service
@Transactional
public class PromptBuilderServiceImpl implements PromptBuilderService {

    private final Logger log = LoggerFactory.getLogger(PromptBuilderServiceImpl.class);

    private final PromptTemplateRepository promptTemplateRepository;
    private final PromptVariableResolver variableResolver;
    private final RagHelper ragHelper;
    private final SystemRoleProperties systemRoleProperties;

    public PromptBuilderServiceImpl(
        PromptTemplateRepository promptTemplateRepository,
        PromptVariableResolver variableResolver,
        RagHelper ragHelper,
        SystemRoleProperties systemRoleProperties
    ) {
        this.promptTemplateRepository = promptTemplateRepository;
        this.variableResolver = variableResolver;
        this.ragHelper = ragHelper;
        this.systemRoleProperties = systemRoleProperties;
    }

    @Override
    public String buildPrompt(String templateKey, Long tenantId, Map<String, Object> variables) {
        log.debug("构建提示词 - 模板键: {}, 租户ID: {}", templateKey, tenantId);

        try {
            // 1. 查找模板
            PromptTemplate template = findTemplate(templateKey, tenantId);
            if (template == null) {
                throw new IllegalArgumentException("未找到模板: " + templateKey);
            }

            // 2. 解析变量
            Map<String, Object> resolvedVariables = variableResolver.resolveVariables(template.getContent(), tenantId, variables);

            // 3. 替换变量
            String prompt = variableResolver.replaceVariables(template.getContent(), resolvedVariables);

            // 4. 添加系统角色定义
            prompt = addSystemRoleDefinition(prompt);

            // 5. 更新使用统计
            updateUsageStatistics(template.getId());

            log.debug("提示词构建完成 - 模板: {}, 长度: {}", templateKey, prompt.length());
            return prompt;
        } catch (Exception e) {
            log.error("构建提示词失败 - 模板键: {}, 错误: {}", templateKey, e.getMessage(), e);
            throw new RuntimeException("构建提示词失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String buildPrompt(PromptTemplateType templateType, Long tenantId, Map<String, Object> variables) {
        log.debug("构建提示词 - 模板类型: {}, 租户ID: {}", templateType, tenantId);

        try {
            // 1. 查找最优模板（租户自定义 > 系统默认）
            List<PromptTemplate> templates = findTemplatesByTypeAndTenant(tenantId, templateType);

            if (templates.isEmpty()) {
                throw new IllegalArgumentException("未找到可用的模板: " + templateType);
            }

            PromptTemplate template = templates.get(0); // 获取优先级最高的模板

            // 2. 解析变量
            Map<String, Object> resolvedVariables = variableResolver.resolveVariables(template.getContent(), tenantId, variables);

            // 3. 替换变量
            String prompt = variableResolver.replaceVariables(template.getContent(), resolvedVariables);

            // 4. 添加系统角色定义
            prompt = addSystemRoleDefinition(prompt);

            // 5. 更新使用统计
            updateUsageStatistics(template.getId());

            log.debug("提示词构建完成 - 模板类型: {}, 使用模板: {}, 长度: {}", templateType, template.getTemplateKey(), prompt.length());
            return prompt;
        } catch (Exception e) {
            log.error("构建提示词失败 - 模板类型: {}, 错误: {}", templateType, e.getMessage(), e);
            throw new RuntimeException("构建提示词失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String buildEnhancedPrompt(String templateKey, Long tenantId, Map<String, Object> variables, boolean enableRagEnhancement) {
        log.debug("构建增强提示词 - 模板键: {}, 租户ID: {}, RAG增强: {}", templateKey, tenantId, enableRagEnhancement);

        try {
            // 1. 构建基础提示词
            String basePrompt = buildPrompt(templateKey, tenantId, variables);

            // 2. 如果启用RAG增强，则进行增强处理
            if (enableRagEnhancement) {
                String context = extractContextFromVariables(variables);
                if (context != null && !context.trim().isEmpty()) {
                    // 使用现有的RAG增强逻辑
                    String enhancedPrompt = ragHelper.enhancePromptForRegulatoryPolicy(
                        context,
                        extractCompanyInfo(variables),
                        tenantId != null ? tenantId.toString() : null
                    );

                    log.debug("RAG增强完成 - 原长度: {}, 增强后长度: {}", basePrompt.length(), enhancedPrompt.length());
                    // 为RAG增强的提示词也添加系统角色定义
                    return addSystemRoleDefinition(enhancedPrompt);
                }
            }

            return basePrompt;
        } catch (Exception e) {
            log.error("构建增强提示词失败 - 模板键: {}, 错误: {}", templateKey, e.getMessage(), e);
            throw new RuntimeException("构建增强提示词失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String buildPrompt(PromptBuildRequest request) {
        log.debug("构建提示词 - 请求: {}", request);

        try {
            String templateKey = request.getTemplateKey();

            // 如果没有指定模板键，则根据模板类型查找
            if (templateKey == null && request.getTemplateType() != null) {
                return buildPrompt(request.getTemplateType(), request.getTenantId(), request.getVariables());
            }

            // 根据是否启用RAG增强选择构建方法
            if (request.isEnableRagEnhancement()) {
                return buildEnhancedPrompt(templateKey, request.getTenantId(), request.getVariables(), true);
            } else {
                return buildPrompt(templateKey, request.getTenantId(), request.getVariables());
            }
        } catch (Exception e) {
            log.error("构建提示词失败 - 请求: {}, 错误: {}", request, e.getMessage(), e);
            throw new RuntimeException("构建提示词失败: " + e.getMessage(), e);
        }
    }

    @Override
    public PromptValidationResult validateVariables(String templateKey, Long tenantId, Map<String, Object> variables) {
        log.debug("验证模板变量 - 模板键: {}, 租户ID: {}", templateKey, tenantId);

        try {
            PromptValidationResult result = new PromptValidationResult();

            // 1. 查找模板
            PromptTemplate template = findTemplate(templateKey, tenantId);
            if (template == null) {
                result.addError("未找到模板: " + templateKey);
                return result;
            }

            // 2. 获取模板变量定义
            Set<PromptTemplateVariable> templateVariables = template.getPromptTemplateVariables();
            if (templateVariables == null || templateVariables.isEmpty()) {
                return result; // 没有变量要求，验证通过
            }

            // 3. 验证必填变量
            for (PromptTemplateVariable templateVar : templateVariables) {
                if (!templateVar.getIsEnabled()) {
                    continue; // 跳过未启用的变量
                }

                String varName = templateVar.getVariableName();

                if (templateVar.getIsRequired()) {
                    if (variables == null || !variables.containsKey(varName) || variables.get(varName) == null) {
                        result.addMissingRequiredVariable(varName);
                        continue;
                    }
                }

                // 4. 验证变量值
                if (variables != null && variables.containsKey(varName)) {
                    Object value = variables.get(varName);
                    boolean isValid = variableResolver.validateVariableValue(
                        varName,
                        templateVar.getVariableType(),
                        value,
                        templateVar.getValidationRule()
                    );

                    if (!isValid) {
                        result.addVariableError(varName, "变量值验证失败");
                    }
                }
            }

            log.debug("变量验证完成 - 模板: {}, 结果: {}", templateKey, result.isValid());
            return result;
        } catch (Exception e) {
            log.error("验证模板变量失败 - 模板键: {}, 错误: {}", templateKey, e.getMessage(), e);
            return PromptValidationResult.failure("验证失败: " + e.getMessage());
        }
    }

    @Override
    public PromptVariableInfo getRequiredVariables(String templateKey, Long tenantId) {
        log.debug("获取模板变量信息 - 模板键: {}, 租户ID: {}", templateKey, tenantId);

        try {
            // 1. 查找模板
            PromptTemplate template = findTemplate(templateKey, tenantId);
            if (template == null) {
                throw new IllegalArgumentException("未找到模板: " + templateKey);
            }

            // 2. 构建变量信息
            List<PromptVariableInfo.VariableInfo> variableInfos = template
                .getPromptTemplateVariables()
                .stream()
                .filter(PromptTemplateVariable::getIsEnabled)
                .sorted(Comparator.comparing(v -> v.getSortOrder() != null ? v.getSortOrder() : 999))
                .map(this::convertToVariableInfo)
                .collect(Collectors.toList());

            return new PromptVariableInfo(template.getTemplateKey(), template.getName(), variableInfos);
        } catch (Exception e) {
            log.error("获取模板变量信息失败 - 模板键: {}, 错误: {}", templateKey, e.getMessage(), e);
            throw new RuntimeException("获取模板变量信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public PromptPreviewResult previewPrompt(String templateKey, Long tenantId, Map<String, Object> variables) {
        log.debug("预览提示词 - 模板键: {}, 租户ID: {}", templateKey, tenantId);

        long startTime = System.currentTimeMillis();

        try {
            // 1. 验证变量
            PromptValidationResult validation = validateVariables(templateKey, tenantId, variables);
            if (!validation.isValid()) {
                return PromptPreviewResult.failure("变量验证失败: " + validation.getErrors());
            }

            // 2. 构建提示词
            String prompt = buildPrompt(templateKey, tenantId, variables);

            // 3. 查找模板信息
            PromptTemplate template = findTemplate(templateKey, tenantId);

            // 4. 构建预览结果
            PromptPreviewResult result = PromptPreviewResult.success(prompt, templateKey);
            result.setTemplateName(template != null ? template.getName() : null);
            result.setTemplateVersion(template != null ? template.getTemplateVersion() : null);
            result.setBuildDuration(System.currentTimeMillis() - startTime);

            // 5. 解析实际使用的变量
            Map<String, Object> resolvedVariables = variableResolver.resolveVariables(template.getContent(), tenantId, variables);
            result.setResolvedVariables(resolvedVariables);

            log.debug("提示词预览完成 - 模板: {}, 耗时: {}ms", templateKey, result.getBuildDuration());
            return result;
        } catch (Exception e) {
            log.error("预览提示词失败 - 模板键: {}, 错误: {}", templateKey, e.getMessage(), e);
            PromptPreviewResult result = PromptPreviewResult.failure(e.getMessage());
            result.setBuildDuration(System.currentTimeMillis() - startTime);
            return result;
        }
    }

    /**
     * 查找模板（优先租户自定义，其次系统默认）
     */
    private PromptTemplate findTemplate(String templateKey, Long tenantId) {
        // 1. 先查找租户自定义模板
        if (tenantId != null) {
            List<PromptTemplate> tenantTemplates = promptTemplateRepository.findByTemplateKeyAndTenantIdAndStatus(
                templateKey,
                tenantId,
                PromptTemplateStatus.PUBLISHED
            );
            if (!tenantTemplates.isEmpty()) {
                return tenantTemplates.get(0);
            }
        }

        // 2. 查找系统默认模板
        List<PromptTemplate> systemTemplates = promptTemplateRepository.findByTemplateKeyAndIsSystemDefaultTrueAndStatus(
            templateKey,
            PromptTemplateStatus.PUBLISHED
        );
        return systemTemplates.isEmpty() ? null : systemTemplates.get(0);
    }

    /**
     * 根据类型和租户查找模板
     */
    private List<PromptTemplate> findTemplatesByTypeAndTenant(Long tenantId, PromptTemplateType templateType) {
        List<PromptTemplate> templates = new ArrayList<>();

        // 1. 先查找租户自定义模板
        if (tenantId != null) {
            templates.addAll(
                promptTemplateRepository.findByTemplateTypeAndTenantIdAndStatus(templateType, tenantId, PromptTemplateStatus.PUBLISHED)
            );
        }

        // 2. 如果没有租户模板，查找系统默认模板
        if (templates.isEmpty()) {
            templates.addAll(
                promptTemplateRepository.findByTemplateTypeAndIsSystemDefaultTrueAndStatus(templateType, PromptTemplateStatus.PUBLISHED)
            );
        }

        return templates;
    }

    /**
     * 更新模板使用统计
     */
    private void updateUsageStatistics(Long templateId) {
        try {
            // 暂时注释掉，因为Repository方法还未实现
            // promptTemplateRepository.incrementUsageCount(templateId);
            log.debug("模板使用统计更新 - 模板ID: {}", templateId);
        } catch (Exception e) {
            log.warn("更新模板使用统计失败 - 模板ID: {}, 错误: {}", templateId, e.getMessage());
        }
    }

    /**
     * 从变量中提取上下文信息
     */
    private String extractContextFromVariables(Map<String, Object> variables) {
        if (variables == null) return null;

        // 尝试从常见的上下文变量中提取
        Object context = variables.get("LEGAL_TEXT");
        if (context == null) context = variables.get("CONTRACT_CONTENT");
        if (context == null) context = variables.get("POLICY_CONTENT");

        return context != null ? context.toString() : null;
    }

    /**
     * 从变量中提取企业信息
     */
    private com.whiskerguard.ai.service.compliance.CompanyInfoDTO extractCompanyInfo(Map<String, Object> variables) {
        if (variables == null) return null;

        // 这里可以根据实际需要从变量中构建CompanyInfoDTO
        // 暂时返回null，让RAG服务使用默认逻辑
        return null;
    }

    /**
     * 转换为变量信息对象
     */
    private PromptVariableInfo.VariableInfo convertToVariableInfo(PromptTemplateVariable variable) {
        PromptVariableInfo.VariableInfo info = new PromptVariableInfo.VariableInfo();
        info.setVariableName(variable.getVariableName());
        info.setDisplayName(variable.getDisplayName());
        info.setDescription(variable.getDescription());
        info.setVariableType(variable.getVariableType());
        info.setRequired(variable.getIsRequired());
        info.setDefaultValue(variable.getDefaultValue());
        info.setValidationRule(variable.getValidationRule());
        info.setExampleValue(variable.getExampleValue());
        info.setSortOrder(variable.getSortOrder());
        return info;
    }

    /**
     * 为提示词添加系统角色定义
     * <p>
     * 在提示词开头添加统一的AI角色身份定义，确保所有AI回复都具有一致的品牌身份。
     *
     * @param originalPrompt 原始提示词
     * @return 添加了系统角色定义的提示词
     */
    private String addSystemRoleDefinition(String originalPrompt) {
        if (systemRoleProperties == null || !systemRoleProperties.isEnabled()) {
            log.debug("系统角色定义功能未启用，返回原始提示词");
            return originalPrompt;
        }

        try {
            String systemRolePrompt = systemRoleProperties.buildSystemRolePrompt();
            if (systemRolePrompt == null || systemRolePrompt.trim().isEmpty()) {
                log.debug("系统角色定义为空，返回原始提示词");
                return originalPrompt;
            }

            // 将系统角色定义添加到提示词开头
            String enhancedPrompt = systemRolePrompt + originalPrompt;

            log.debug("已添加系统角色定义 - 原长度: {}, 增强后长度: {}", originalPrompt.length(), enhancedPrompt.length());

            return enhancedPrompt;
        } catch (Exception e) {
            log.warn("添加系统角色定义失败，使用原始提示词，错误: {}", e.getMessage());
            return originalPrompt;
        }
    }
}
