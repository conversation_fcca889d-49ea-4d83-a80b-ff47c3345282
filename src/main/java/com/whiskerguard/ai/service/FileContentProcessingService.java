/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：FileContentProcessingService.java
 * 包    名：com.whiskerguard.ai.service
 * 描    述：文件内容处理服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service;

import com.whiskerguard.ai.client.GeneralServiceClient;
import com.whiskerguard.ai.client.dto.FileOperationRequestDTO;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.AiStreamRequestDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 文件内容处理服务
 * <p>
 * 负责处理AI调用请求中的文件内容，包括：
 * 1. 从metadata中提取cosFileName
 * 2. 调用通用服务读取腾讯云COS文件内容
 * 3. 将文件内容与用户提示词合并
 * 4. 提供文件存在性检查
 * 5. 错误处理和降级策略
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class FileContentProcessingService {

    private static final Logger log = LoggerFactory.getLogger(FileContentProcessingService.class);

    /**
     * metadata中cosFileName字段的键名
     */
    private static final String COS_FILE_NAME_KEY = "cosFileName";

    /**
     * 通用服务客户端，用于调用文件读取接口
     */
    private final GeneralServiceClient generalServiceClient;

    /**
     * 构造函数
     * 
     * @param generalServiceClient 通用服务客户端
     */
    public FileContentProcessingService(GeneralServiceClient generalServiceClient) {
        this.generalServiceClient = generalServiceClient;
    }

    /**
     * 处理AI调用请求中的文件内容（非流式）
     * <p>
     * 检查metadata中是否包含cosFileName，如果包含则读取文件内容并与提示词合并。
     * 
     * @param dto 原始AI调用请求DTO
     * @return 处理后的AI调用请求DTO，如果有文件则提示词已包含文件内容
     */
    public AiInvocationRequestDTO processFileContent(AiInvocationRequestDTO dto) {
        log.debug("开始处理AI调用请求中的文件内容，租户ID: {}, 员工ID: {}", dto.getTenantId(), dto.getEmployeeId());

        // 检查是否包含文件名
        String cosFileName = extractCosFileName(dto.getMetadata());
        if (cosFileName == null || cosFileName.trim().isEmpty()) {
            log.debug("请求中未包含文件名，跳过文件处理");
            return dto;
        }

        log.info("检测到文件名: {}, 开始读取文件内容", cosFileName);

        try {
            // 读取文件内容
            String fileContent = readFileContent(cosFileName, dto.getTenantId());
            
            if (fileContent == null || fileContent.trim().isEmpty()) {
                log.warn("文件内容为空，文件名: {}", cosFileName);
                return dto;
            }

            // 合并文件内容和用户提示词
            String enhancedPrompt = combinePromptWithFileContent(dto.getPrompt(), fileContent, cosFileName);
            
            // 创建新的DTO，使用增强后的提示词
            AiInvocationRequestDTO enhancedDto = new AiInvocationRequestDTO(
                dto.getToolKey(),
                enhancedPrompt,
                dto.getMetadata(),
                dto.getTenantId(),
                dto.getEmployeeId(),
                dto.getTemplateKey(),
                dto.getTemplateType(),
                dto.getTemplateVariables(),
                dto.isUseTemplate()
            );

            // 复制其他字段
            enhancedDto.setConversationId(dto.getConversationId());
            enhancedDto.setEnableContext(dto.isEnableContext());
            enhancedDto.setMaxContextTurns(dto.getMaxContextTurns());

            log.info("文件内容处理完成，原始提示词长度: {}, 增强后长度: {}", 
                dto.getPrompt() != null ? dto.getPrompt().length() : 0, 
                enhancedPrompt.length());

            return enhancedDto;

        } catch (Exception e) {
            log.error("处理文件内容时发生错误，文件名: {}, 错误: {}", cosFileName, e.getMessage(), e);
            // 发生错误时返回原始DTO，不影响正常的AI调用
            return dto;
        }
    }

    /**
     * 处理AI流式调用请求中的文件内容
     * <p>
     * 检查metadata中是否包含cosFileName，如果包含则读取文件内容并与提示词合并。
     * 
     * @param dto 原始AI流式调用请求DTO
     * @return 处理后的AI流式调用请求DTO，如果有文件则提示词已包含文件内容
     */
    public AiStreamRequestDTO processFileContent(AiStreamRequestDTO dto) {
        log.debug("开始处理AI流式调用请求中的文件内容，租户ID: {}, 员工ID: {}", dto.getTenantId(), dto.getEmployeeId());

        // 检查是否包含文件名
        String cosFileName = extractCosFileName(dto.getMetadata());
        if (cosFileName == null || cosFileName.trim().isEmpty()) {
            log.debug("请求中未包含文件名，跳过文件处理");
            return dto;
        }

        log.info("检测到文件名: {}, 开始读取文件内容", cosFileName);

        try {
            // 读取文件内容
            String fileContent = readFileContent(cosFileName, dto.getTenantId());
            
            if (fileContent == null || fileContent.trim().isEmpty()) {
                log.warn("文件内容为空，文件名: {}", cosFileName);
                return dto;
            }

            // 合并文件内容和用户提示词
            String enhancedPrompt = combinePromptWithFileContent(dto.getPrompt(), fileContent, cosFileName);
            
            // 创建新的DTO，使用增强后的提示词
            AiStreamRequestDTO enhancedDto = new AiStreamRequestDTO(
                dto.getToolKey(),
                enhancedPrompt,
                dto.getMetadata(),
                dto.getTenantId(),
                dto.getEmployeeId(),
                dto.getTemplateKey(),
                dto.getTemplateType(),
                dto.getTemplateVariables(),
                dto.isUseTemplate()
            );

            // 复制其他字段
            enhancedDto.setStreaming(dto.isStreaming());
            enhancedDto.setConversationId(dto.getConversationId());
            enhancedDto.setEnableContext(dto.isEnableContext());
            enhancedDto.setMaxContextTurns(dto.getMaxContextTurns());

            log.info("文件内容处理完成，原始提示词长度: {}, 增强后长度: {}", 
                dto.getPrompt() != null ? dto.getPrompt().length() : 0, 
                enhancedPrompt.length());

            return enhancedDto;

        } catch (Exception e) {
            log.error("处理文件内容时发生错误，文件名: {}, 错误: {}", cosFileName, e.getMessage(), e);
            // 发生错误时返回原始DTO，不影响正常的AI调用
            return dto;
        }
    }

    /**
     * 检查文件是否存在
     * <p>
     * 用于在读取文件内容前进行预检查。
     * 
     * @param cosFileName 腾讯云COS中的文件名
     * @param tenantId 租户ID
     * @return 文件是否存在
     */
    public boolean checkFileExists(String cosFileName, Long tenantId) {
        if (cosFileName == null || cosFileName.trim().isEmpty() || tenantId == null) {
            return false;
        }

        try {
            Boolean exists = generalServiceClient.checkFileExists(cosFileName, tenantId);
            return exists != null && exists;
        } catch (Exception e) {
            log.error("检查文件存在性时发生错误，文件名: {}, 租户ID: {}, 错误: {}", cosFileName, tenantId, e.getMessage());
            return false;
        }
    }

    /**
     * 从metadata中提取cosFileName
     * 
     * @param metadata 元数据Map
     * @return cosFileName，如果不存在则返回null
     */
    private String extractCosFileName(Map<String, Object> metadata) {
        if (metadata == null || metadata.isEmpty()) {
            return null;
        }

        Object cosFileNameObj = metadata.get(COS_FILE_NAME_KEY);
        if (cosFileNameObj == null) {
            return null;
        }

        return cosFileNameObj.toString().trim();
    }

    /**
     * 读取文件内容
     * 
     * @param cosFileName 腾讯云COS中的文件名
     * @param tenantId 租户ID
     * @return 文件内容，如果读取失败则返回null
     */
    private String readFileContent(String cosFileName, Long tenantId) {
        try {
            // 先检查文件是否存在
            if (!checkFileExists(cosFileName, tenantId)) {
                log.warn("文件不存在，文件名: {}, 租户ID: {}", cosFileName, tenantId);
                return null;
            }

            // 读取文件内容
            String content = generalServiceClient.readFileContent(cosFileName, tenantId);
            log.debug("成功读取文件内容，文件名: {}, 内容长度: {}", cosFileName, 
                content != null ? content.length() : 0);
            
            return content;

        } catch (Exception e) {
            log.error("读取文件内容时发生错误，文件名: {}, 租户ID: {}, 错误: {}", cosFileName, tenantId, e.getMessage());
            return null;
        }
    }

    /**
     * 合并用户提示词和文件内容
     * 
     * @param userPrompt 用户原始提示词
     * @param fileContent 文件内容
     * @param fileName 文件名（用于提示）
     * @return 合并后的提示词
     */
    private String combinePromptWithFileContent(String userPrompt, String fileContent, String fileName) {
        StringBuilder combinedPrompt = new StringBuilder();

        // 添加文件内容部分
        combinedPrompt.append("【文件内容】\n");
        combinedPrompt.append("文件名: ").append(fileName).append("\n");
        combinedPrompt.append("文件内容:\n");
        combinedPrompt.append(fileContent);
        combinedPrompt.append("\n\n");

        // 添加用户提示词部分
        combinedPrompt.append("【用户问题】\n");
        if (userPrompt != null && !userPrompt.trim().isEmpty()) {
            combinedPrompt.append(userPrompt);
        } else {
            combinedPrompt.append("请分析上述文件内容。");
        }

        return combinedPrompt.toString();
    }
}
