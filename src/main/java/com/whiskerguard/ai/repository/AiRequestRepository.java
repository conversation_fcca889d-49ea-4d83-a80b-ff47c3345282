package com.whiskerguard.ai.repository;

import com.whiskerguard.ai.domain.AiRequest;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the AiRequest entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AiRequestRepository extends JpaRepository<AiRequest, Long> {
    /**
     * 根据员工ID分页查询AI请求列表
     * @param employeeId 员工ID
     * @param pageable 分页参数
     * @return 分页的AI请求列表
     */
    Page<AiRequest> findByEmployeeId(Long employeeId, Pageable pageable);

    /**
     * 根据员工ID和请求状态分页查询AI请求列表
     * @param employeeId 员工ID
     * @param status 请求状态
     * @param pageable 分页参数
     * @return 分页的AI请求列表
     */
    Page<AiRequest> findByEmployeeIdAndStatus(
        Long employeeId,
        com.whiskerguard.ai.domain.enumeration.RequestStatus status,
        Pageable pageable
    );

    /**
     * 根据员工ID和工具类型分页查询AI请求列表
     * @param employeeId 员工ID
     * @param toolType 工具类型
     * @param pageable 分页参数
     * @return 分页的AI请求列表
     */
    Page<AiRequest> findByEmployeeIdAndToolType(Long employeeId, String toolType, Pageable pageable);

    /**
     * 根据员工ID查询AI请求列表（不分页）
     * @param employeeId 员工ID
     * @return AI请求列表
     */
    List<AiRequest> findByEmployeeIdOrderByCreatedAtDesc(Long employeeId);

    /**
     * 根据对话ID、租户ID和员工ID查询对话历史记录，按序号排序
     * @param conversationId 对话会话ID
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @return 对话历史记录列表，按序号升序排列
     */
    List<AiRequest> findByConversationIdAndTenantIdAndEmployeeIdOrderByConversationSequenceAsc(
        String conversationId,
        Long tenantId,
        Long employeeId
    );

    /**
     * 根据对话ID、租户ID和员工ID查询对话历史记录，按序号排序，限制数量
     * @param conversationId 对话会话ID
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @param pageable 分页参数
     * @return 对话历史记录列表，按序号升序排列
     */
    Page<AiRequest> findByConversationIdAndTenantIdAndEmployeeIdOrderByConversationSequenceAsc(
        String conversationId,
        Long tenantId,
        Long employeeId,
        Pageable pageable
    );

    /**
     * 获取对话中的最大序号
     * @param conversationId 对话会话ID
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @return 最大序号，如果没有记录则返回null
     */
    @Query(
        "SELECT MAX(a.conversationSequence) FROM AiRequest a WHERE a.conversationId = :conversationId AND a.tenantId = :tenantId AND a.employeeId = :employeeId"
    )
    Optional<Integer> findMaxSequenceByConversationId(
        @Param("conversationId") String conversationId,
        @Param("tenantId") Long tenantId,
        @Param("employeeId") Long employeeId
    );

    /**
     * 获取用户的对话列表（去重）
     * @param tenantId 租户ID
     * @param employeeId 员工ID
     * @return 对话ID列表，按创建时间倒序
     */
    @Query(
        "SELECT DISTINCT a.conversationId FROM AiRequest a WHERE a.tenantId = :tenantId AND a.employeeId = :employeeId AND a.conversationId IS NOT NULL ORDER BY a.createdAt DESC"
    )
    List<String> findConversationIdsByTenantIdAndEmployeeId(@Param("tenantId") Long tenantId, @Param("employeeId") Long employeeId);
}
